<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Progress Bars Test</title>
    <link rel="stylesheet" href="./styles/dashboard.css">
</head>
<body>
    <div style="padding: 20px; background: #f5f5f5; min-height: 100vh;">
        <h1>🔧 Quick Progress Bars Test</h1>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Account Status Progress Bars</h3>
            
            <div class="account-status">
                <div class="account-status-metrics">
                    <div class="metric-item">
                        <div class="metric-header">
                            <span class="metric-label">Designs</span>
                            <span class="metric-percentage">89%</span>
                        </div>
                        <div class="metric-subtext">17,748 of 20000</div>
                        <div class="progress-bar">
                            <div class="progress-track"></div>
                            <div class="progress-fill" style="width: 89%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-header">
                            <span class="metric-label">Products</span>
                            <span class="metric-percentage">70%</span>
                        </div>
                        <div class="metric-subtext">1,129,440 of 1,620,000</div>
                        <div class="progress-bar">
                            <div class="progress-track"></div>
                            <div class="progress-fill" style="width: 70%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-header">
                            <span class="metric-label">Today's Quota</span>
                            <span class="metric-percentage">25%</span>
                        </div>
                        <div class="metric-subtext">444 of 2000</div>
                        <div class="progress-bar">
                            <div class="progress-track"></div>
                            <div class="progress-fill" style="width: 25%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Test Controls</h3>
            <button onclick="testProgressAnimation()" style="background: #470CED; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px;">
                Test Progress Animation
            </button>
            <button onclick="resetProgressBars()" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px;">
                Reset Progress Bars
            </button>
            <button onclick="debugProgressBars()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px;">
                Debug Progress Bars
            </button>
            
            <div id="debug-output" style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
        </div>
    </div>

    <script>
        function testProgressAnimation() {
            console.log('🔧 Testing progress bar animation...');
            const progressBars = document.querySelectorAll('.progress-fill');
            const originalWidths = ['89%', '70%', '25%'];
            
            document.getElementById('debug-output').textContent = `Found ${progressBars.length} progress bars to animate...\n`;
            
            progressBars.forEach((bar, index) => {
                const width = originalWidths[index];
                console.log(`📊 Progress bar ${index}: animating to ${width}`);
                
                // Reset to 0
                bar.style.width = '0';
                
                // Animate back with staggered timing
                setTimeout(() => {
                    bar.style.width = width;
                    console.log(`✅ Progress bar ${index} animated to ${width}`);
                    
                    // Update debug output
                    const currentOutput = document.getElementById('debug-output').textContent;
                    document.getElementById('debug-output').textContent = currentOutput + `Progress bar ${index + 1} animated to ${width}\n`;
                }, 100 + (index * 100));
            });
        }

        function resetProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            const originalWidths = ['89%', '70%', '25%'];
            
            progressBars.forEach((bar, index) => {
                bar.style.width = originalWidths[index];
            });
            
            document.getElementById('debug-output').textContent = 'Progress bars reset to original widths.\n';
        }

        function debugProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            let debugInfo = `Found ${progressBars.length} progress bars:\n\n`;
            
            progressBars.forEach((bar, index) => {
                const computedStyle = window.getComputedStyle(bar);
                debugInfo += `Progress Bar ${index + 1}:\n`;
                debugInfo += `  - Inline width: ${bar.style.width}\n`;
                debugInfo += `  - Computed width: ${computedStyle.width}\n`;
                debugInfo += `  - Display: ${computedStyle.display}\n`;
                debugInfo += `  - Background: ${computedStyle.background}\n`;
                debugInfo += `  - Parent element: ${bar.parentElement.className}\n\n`;
            });
            
            document.getElementById('debug-output').textContent = debugInfo;
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Page loaded, testing progress bars...');
            setTimeout(() => {
                testProgressAnimation();
            }, 500);
        });
    </script>
</body>
</html>
