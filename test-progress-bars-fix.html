<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Bars Fix Test</title>
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(96, 111, 149, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-track {
            width: 100%;
            height: 100%;
            background: rgba(96, 111, 149, 0.1);
            position: absolute;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #470CED 0%, #6B46C1 100%);
            border-radius: 4px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        .metric-item {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .metric-label {
            font-size: 14px;
            font-weight: 500;
            color: #232F3E;
        }
        .metric-percentage {
            font-size: 14px;
            font-weight: 600;
            color: #470CED;
        }
        .metric-subtext {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        .test-button {
            background: #470CED;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3a0bc4;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Progress Bars Fix Test</h1>
    
    <div class="test-container">
        <h3>Current Progress Bars (Account Status Style)</h3>
        
        <div class="metric-item">
            <div class="metric-header">
                <span class="metric-label">Designs</span>
                <span class="metric-percentage">89%</span>
            </div>
            <div class="metric-subtext">17,748 of 20000</div>
            <div class="progress-bar">
                <div class="progress-track"></div>
                <div class="progress-fill" style="width: 89%"></div>
            </div>
        </div>

        <div class="metric-item">
            <div class="metric-header">
                <span class="metric-label">Products</span>
                <span class="metric-percentage">70%</span>
            </div>
            <div class="metric-subtext">1,129,440 of 1,620,000</div>
            <div class="progress-bar">
                <div class="progress-track"></div>
                <div class="progress-fill" style="width: 70%"></div>
            </div>
        </div>

        <div class="metric-item">
            <div class="metric-header">
                <span class="metric-label">Today's Quota</span>
                <span class="metric-percentage">25%</span>
            </div>
            <div class="metric-subtext">444 of 2000</div>
            <div class="progress-bar">
                <div class="progress-track"></div>
                <div class="progress-fill" style="width: 25%"></div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h3>Test Controls</h3>
        <button class="test-button" onclick="testOriginalAnimation()">Test Original Animation</button>
        <button class="test-button" onclick="testOptimizedAnimation()">Test Optimized Animation</button>
        <button class="test-button" onclick="debugProgressBars()">Debug Progress Bars</button>
        <button class="test-button" onclick="resetProgressBars()">Reset Progress Bars</button>
        
        <div id="debug-output" class="debug-info"></div>
    </div>

    <script>
        function debugProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            const debugOutput = document.getElementById('debug-output');
            
            let debugInfo = `Found ${progressBars.length} progress bars:\n\n`;
            
            progressBars.forEach((bar, index) => {
                const computedStyle = window.getComputedStyle(bar);
                debugInfo += `Progress Bar ${index + 1}:\n`;
                debugInfo += `  - Inline width: ${bar.style.width}\n`;
                debugInfo += `  - Computed width: ${computedStyle.width}\n`;
                debugInfo += `  - Display: ${computedStyle.display}\n`;
                debugInfo += `  - Visibility: ${computedStyle.visibility}\n`;
                debugInfo += `  - Background: ${computedStyle.background}\n`;
                debugInfo += `  - Height: ${computedStyle.height}\n\n`;
            });
            
            debugOutput.textContent = debugInfo;
        }

        function resetProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            const originalWidths = ['89%', '70%', '25%'];
            
            progressBars.forEach((bar, index) => {
                bar.style.width = originalWidths[index];
            });
            
            document.getElementById('debug-output').textContent = 'Progress bars reset to original widths.';
        }

        function testOriginalAnimation() {
            const progressBars = document.querySelectorAll('.progress-fill');
            const originalWidths = ['89%', '70%', '25%'];
            
            // Reset to 0
            progressBars.forEach(bar => {
                bar.style.width = '0';
            });
            
            // Animate back
            setTimeout(() => {
                progressBars.forEach((bar, index) => {
                    bar.style.width = originalWidths[index];
                });
            }, 100);
            
            document.getElementById('debug-output').textContent = 'Original animation test completed.';
        }

        function testOptimizedAnimation() {
            // Simulate the optimized animation approach
            const progressBars = document.querySelectorAll('.progress-fill');
            const originalWidths = ['89%', '70%', '25%'];
            
            // Store original widths and reset to 0
            progressBars.forEach((bar, index) => {
                bar.style.width = '0';
            });
            
            // Use requestAnimationFrame for smooth animation
            requestAnimationFrame(() => {
                setTimeout(() => {
                    // Simulate batched operations
                    const operations = [];
                    
                    progressBars.forEach((bar, index) => {
                        operations.push({
                            element: bar,
                            width: originalWidths[index]
                        });
                    });
                    
                    // Apply all operations at once
                    operations.forEach(op => {
                        op.element.style.width = op.width;
                    });
                    
                    document.getElementById('debug-output').textContent = `Optimized animation test completed with ${operations.length} batched operations.`;
                }, 100);
            });
        }

        // Initialize debug on page load
        document.addEventListener('DOMContentLoaded', () => {
            debugProgressBars();
        });
    </script>
</body>
</html>
