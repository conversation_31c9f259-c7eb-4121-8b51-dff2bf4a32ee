<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Snap Dashboard - Logging Control Test</title>
  <link rel="stylesheet" href="snapapp.css">
  <style>
    .logging-control-panel {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 2px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      z-index: 10000;
      font-family: 'Amazon Ember', sans-serif;
      min-width: 300px;
    }
    
    .logging-control-panel h3 {
      margin: 0 0 15px 0;
      color: #333;
    }
    
    .log-level-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 15px;
    }
    
    .log-level-btn {
      padding: 8px 12px;
      border: 1px solid #ddd;
      background: #f5f5f5;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .log-level-btn:hover {
      background: #e0e0e0;
    }
    
    .log-level-btn.active {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
    
    .log-stats {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 10px;
    }
    
    .console-info {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <!-- Logger (load first) -->
  <script src="utils/logger.js"></script>
  
  <!-- Performance Optimization Scripts -->
  <script src="performance-optimizations/event-cleanup-manager.js"></script>
  <script src="performance-optimizations/data-cache-manager.js"></script>
  <script src="performance-optimizations/dom-optimizer.js"></script>
  <script src="performance-optimizations/memory-monitor.js"></script>
  <script src="performance-optimizations/realtime-data-manager.js"></script>

  <!-- Logging Control Panel -->
  <div class="logging-control-panel">
    <h3>🔧 Snap Dashboard Logging Control</h3>
    
    <div class="log-level-buttons">
      <button class="log-level-btn" data-level="0">0 - Silent (No logs)</button>
      <button class="log-level-btn active" data-level="2">2 - Warn (Default - Errors & Warnings only)</button>
      <button class="log-level-btn" data-level="3">3 - Info (Basic information)</button>
      <button class="log-level-btn" data-level="4">4 - Debug (Detailed debugging)</button>
      <button class="log-level-btn" data-level="5">5 - Verbose (All logs)</button>
    </div>
    
    <div class="console-info">
      <strong>Console Filter:</strong> If you still see "hidden entries", check your browser console's filter settings. Look for a filter dropdown or "Default levels" setting.
    </div>
    
    <div class="log-stats" id="logStats">
      Loading stats...
    </div>
    
    <button onclick="testLogging()" style="width: 100%; padding: 8px; margin-top: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
      Test Logging at Current Level
    </button>
  </div>

  <!-- Main App Container -->
  <div id="app"></div>

  <script>
    // Initialize global instances
    window.EventCleanupManager = new EventCleanupManager();
    window.DataCacheManager = new DataCacheManager();
    window.DOMOptimizer = new DOMOptimizer();
    window.MemoryMonitor = new MemoryMonitor();
    window.RealTimeDataManager = new RealTimeDataManager();

    // Update stats display
    function updateStats() {
      const stats = window.getLogStats();
      document.getElementById('logStats').innerHTML = `
        <strong>Current Level:</strong> ${stats.levelName} (${stats.logLevel})<br>
        <strong>Logs Generated:</strong> ${stats.logCount}<br>
        <strong>Uptime:</strong> ${stats.uptime}
      `;
    }

    // Handle log level changes
    document.querySelectorAll('.log-level-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const level = parseInt(btn.dataset.level);
        window.setLogLevel(level);
        
        // Update active button
        document.querySelectorAll('.log-level-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        updateStats();
      });
    });

    // Test logging function
    function testLogging() {
      console.log('🧪 Testing logging at current level...');
      
      // Test different log levels
      window.SnapLogger.error('Test error message');
      window.SnapLogger.warn('Test warning message');
      window.SnapLogger.info('Test info message');
      window.SnapLogger.debug('Test debug message');
      window.SnapLogger.verbose('Test verbose message');
      
      // Trigger some performance script activity
      window.EventCleanupManager.addEventListener(document.body, 'test', () => {}, {});
      window.DataCacheManager.getData('test', {}, async () => ({ test: 'data' }));
      
      updateStats();
      
      console.log('🧪 Test completed. Check console for output based on current log level.');
    }

    // Start some background activity to demonstrate logging
    setTimeout(() => {
      window.MemoryMonitor.startMonitoring();
      window.RealTimeDataManager.startRealTimeUpdates();
      updateStats();
    }, 1000);

    // Update stats every 5 seconds
    setInterval(updateStats, 5000);
    
    // Initial stats update
    updateStats();
  </script>
</body>
</html>
