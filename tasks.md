# Current Task: Fix Tooltip Scroll Behavior

## Task Description
Make all tooltips function like metric-remaining tooltips - when scrolling away from an element, the tooltip should disappear immediately instead of staying visible.

## Analysis Completed
✅ **Root Cause Identified**:
- Metric-remaining tooltips use the global tooltip system (snapapp.js) which has immediate + delayed scroll handling
- Other tooltips (analyze btn, edit btn, badges) use custom tooltip system (dashboard.js) which only had single scroll handling
- Global system calls `updateTooltipPositions()` immediately on scroll, then again after 16ms
- Custom system only called `handleCustomTooltipScroll()` once per scroll event

## Implementation Tasks
✅ **Task 1**: Updated custom tooltip scroll handler to match global system responsiveness
- Added throttled scroll handling with immediate + delayed pattern
- Split logic into `handleCustomTooltipScroll()` and `checkAndHideCustomTooltips()`
- Added 16ms timeout to match global system (~60fps)

## Testing Tasks
- [ ] **Task 2**: Test tooltip behavior on analyze buttons during scroll
- [ ] **Task 3**: Test tooltip behavior on edit buttons during scroll
- [ ] **Task 4**: Test tooltip behavior on badges in listings during scroll
- [ ] **Task 5**: Verify metric-remaining tooltips still work correctly
- [ ] **Task 6**: Test on different scroll speeds and directions

## Expected Result
All tooltips should now disappear immediately when scrolling away from their trigger elements, matching the behavior of metric-remaining tooltips.



